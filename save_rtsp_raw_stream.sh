#!/bin/bash
# ─────────────────────────────────────────────
# Script: save_rtsp_raw_stream.sh
# Purpose:
#   - Save raw RTSP stream directly to file
#   - No compression, re-encoding, or processing
#   - Takes RTSP URL and duration as arguments
# ─────────────────────────────────────────────

# 📥 Parse input arguments
RTSP_URL="$1"
CAPTURE_DURATION="$2"
SHARED_TIMESTAMP="$3"

if [ -z "$RTSP_URL" ] || [ -z "$CAPTURE_DURATION" ]; then
    echo "❗ Usage: $0 <RTSP_URL> <DURATION (HH:MM:SS)> [TIMESTAMP]"
    exit 1
fi

# 📂 Output folders
mkdir -p original/stream
mkdir -p logs

# 🕒 Timestamped filenames (use shared timestamp if provided)
if [ -n "$SHARED_TIMESTAMP" ]; then
    TIMESTAMP="$SHARED_TIMESTAMP"
else
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
fi
RAW_FILE="original/stream/stream_${TIMESTAMP}.ts"
HLS_PLAYLIST="original/stream/stream_${TIMESTAMP}.m3u8"
HLS_SEGMENT_PATTERN="original/stream/stream_${TIMESTAMP}_%03d.ts"
LOG_FILE="logs/raw_capture_${TIMESTAMP}.log"

# 📡 Start capture with HLS output
echo "🎥 Capturing raw RTSP stream with HLS output..."
echo "📡 Source: $RTSP_URL" | tee -a "$LOG_FILE"
echo "⏱ Duration: $CAPTURE_DURATION" | tee -a "$LOG_FILE"
echo "💾 Saving to: $RAW_FILE" | tee -a "$LOG_FILE"
echo "📺 HLS playlist: $HLS_PLAYLIST" | tee -a "$LOG_FILE"

# Create single file output first
ffmpeg -y -hide_banner -loglevel info \
    -rtsp_transport tcp \
    -i "$RTSP_URL" \
    -t "$CAPTURE_DURATION" \
    -c copy \
    -f mpegts "$RAW_FILE" \
    2>> "$LOG_FILE" &

# Create HLS playlist output in parallel
ffmpeg -y -hide_banner -loglevel info \
    -rtsp_transport tcp \
    -i "$RTSP_URL" \
    -t "$CAPTURE_DURATION" \
    -c copy \
    -f hls \
    -hls_time 6 \
    -hls_list_size 10 \
    -hls_flags delete_segments+append_list \
    "$HLS_PLAYLIST" \
    2>> "$LOG_FILE" &

# Wait for both processes to complete
wait

# ✅ Confirmation
if [ $? -eq 0 ] && [ -f "$RAW_FILE" ]; then
    echo "✅ Raw stream saved: $RAW_FILE" | tee -a "$LOG_FILE"

    # Check if HLS playlist was created
    if [ -f "$HLS_PLAYLIST" ]; then
        echo "✅ HLS playlist created: $HLS_PLAYLIST" | tee -a "$LOG_FILE"
    else
        echo "⚠️ HLS playlist not created, but single file capture succeeded" | tee -a "$LOG_FILE"
    fi
else
    echo "❌ Failed to capture stream." | tee -a "$LOG_FILE"
fi


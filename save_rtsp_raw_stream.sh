#!/bin/bash
# ─────────────────────────────────────────────
# Script: save_rtsp_raw_stream.sh
# Purpose:
#   - Save raw RTSP stream directly to file
#   - No compression, re-encoding, or processing
#   - Takes RTSP URL and duration as arguments
# ─────────────────────────────────────────────

# 📥 Parse input arguments
RTSP_URL="$1"
CAPTURE_DURATION="$2"

if [ -z "$RTSP_URL" ] || [ -z "$CAPTURE_DURATION" ]; then
    echo "❗ Usage: $0 <RTSP_URL> <DURATION (HH:MM:SS)>"
    exit 1
fi

# 📂 Output folders
mkdir -p original/stream
mkdir -p logs

# 🕒 Timestamped filenames
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RAW_FILE="original/stream/stream_${TIMESTAMP}.ts"
LOG_FILE="logs/raw_capture_${TIMESTAMP}.log"

# 📡 Start capture
echo "🎥 Capturing raw RTSP stream..."
echo "📡 Source: $RTSP_URL" | tee -a "$LOG_FILE"
echo "⏱ Duration: $CAPTURE_DURATION" | tee -a "$LOG_FILE"
echo "💾 Saving to: $RAW_FILE" | tee -a "$LOG_FILE"

ffmpeg -y -hide_banner -loglevel info \
    -rtsp_transport tcp \
    -i "$RTSP_URL" \
    -t "$CAPTURE_DURATION" \
    -c copy \
    -f mpegts "$RAW_FILE" 2>> "$LOG_FILE"

# ✅ Confirmation
if [ $? -eq 0 ] && [ -f "$RAW_FILE" ]; then
    echo "✅ Raw stream saved: $RAW_FILE" | tee -a "$LOG_FILE"
else
    echo "❌ Failed to capture stream." | tee -a "$LOG_FILE"
fi


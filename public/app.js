class RTSPDashboard {
    constructor() {
        this.ws = null;
        this.currentSessionId = null;
        this.startTime = null;
        this.estimatedDuration = 0;
        this.progressInterval = null;
        
        this.initializeElements();
        this.bindEvents();
        this.connectWebSocket();
    }

    initializeElements() {
        // Form elements
        this.form = document.getElementById('compressionForm');
        this.rtspUrlInput = document.getElementById('rtspUrl');
        this.durationInput = document.getElementById('duration');
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');

        // Status elements
        this.currentStatus = document.getElementById('currentStatus');
        this.sessionIdDisplay = document.getElementById('sessionId');
        this.configuredDuration = document.getElementById('configuredDuration');

        // Metrics elements
        this.rawSize = document.getElementById('rawSize');
        this.compressedSize = document.getElementById('compressedSize');
        this.compressionRatio = document.getElementById('compressionRatio');
        this.bandwidthSavings = document.getElementById('bandwidthSavings');

        // Progress elements
        this.progressSection = document.querySelector('.progress-section');
        this.progressFill = document.getElementById('progressFill');
        this.progressPercent = document.getElementById('progressPercent');
        this.timeRemaining = document.getElementById('timeRemaining');

        // Logs and report
        this.logsContainer = document.getElementById('logsContainer');
        this.reportSection = document.querySelector('.report-section');
        this.finalReport = document.getElementById('finalReport');
    }

    bindEvents() {
        this.form.addEventListener('submit', (e) => this.handleStartCompression(e));
        this.stopBtn.addEventListener('click', () => this.handleStopCompression());
    }

    connectWebSocket() {
        const wsUrl = `ws://${window.location.hostname}:8080`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            this.addLog('WebSocket connected', 'info');
        };

        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleWebSocketMessage(message);
        };

        this.ws.onclose = () => {
            this.addLog('WebSocket disconnected. Attempting to reconnect...', 'warning');
            setTimeout(() => this.connectWebSocket(), 3000);
        };

        this.ws.onerror = (error) => {
            this.addLog('WebSocket error occurred', 'error');
        };
    }

    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'progress':
                this.updateMetrics(message.data);
                break;
            case 'completed':
                this.handleProcessCompleted(message.data);
                break;
            case 'error':
                this.handleProcessError(message.data);
                break;
            case 'log':
                this.addLog(message.data.message, message.data.level || 'info');
                break;
        }
    }

    async handleStartCompression(e) {
        e.preventDefault();

        const rtspUrl = this.rtspUrlInput.value.trim();
        const durationMinutes = parseFloat(this.durationInput.value);

        if (!rtspUrl || !durationMinutes) {
            this.addLog('Please enter both RTSP URL and duration', 'error');
            return;
        }

        try {
            this.setUIState('starting');
            
            const response = await fetch('/api/start-compression', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    rtspUrl,
                    durationMinutes
                })
            });

            const result = await response.json();

            if (result.success) {
                this.currentSessionId = result.sessionId;
                this.startTime = new Date();
                this.estimatedDuration = durationMinutes * 60 * 1000; // Convert to milliseconds
                
                this.setUIState('running');
                this.updateStatus('Running', result.sessionId, result.duration);
                this.addLog(`Compression started - Session: ${result.sessionId}`, 'info');
                this.addLog(`Duration: ${result.duration}`, 'info');
                
                this.startProgressTracking();
            } else {
                throw new Error(result.error || 'Failed to start compression');
            }
        } catch (error) {
            this.addLog(`Error: ${error.message}`, 'error');
            this.setUIState('ready');
        }
    }

    async handleStopCompression() {
        if (!this.currentSessionId) return;

        try {
            const response = await fetch(`/api/stop-compression/${this.currentSessionId}`, {
                method: 'POST'
            });

            const result = await response.json();
            
            if (result.success) {
                this.addLog('Process stopped by user', 'warning');
                this.setUIState('ready');
                this.stopProgressTracking();
            }
        } catch (error) {
            this.addLog(`Error stopping process: ${error.message}`, 'error');
        }
    }

    updateMetrics(data) {
        this.rawSize.textContent = data.originalSizeFormatted;
        this.compressedSize.textContent = data.encodedSizeFormatted;
        this.compressionRatio.textContent = `${data.compressionRatio}%`;
        this.bandwidthSavings.textContent = `${data.compressionRatio}%`;
    }

    handleProcessCompleted(data) {
        this.addLog(`Process completed with exit code: ${data.exitCode}`, data.success ? 'info' : 'error');
        this.setUIState('completed');
        this.stopProgressTracking();
        
        if (data.success) {
            this.generateFinalReport();
        }
    }

    handleProcessError(data) {
        this.addLog(`Process error: ${data.error}`, 'error');
        this.setUIState('ready');
        this.stopProgressTracking();
    }

    setUIState(state) {
        switch (state) {
            case 'ready':
                this.startBtn.style.display = 'inline-flex';
                this.stopBtn.style.display = 'none';
                this.startBtn.disabled = false;
                this.progressSection.style.display = 'none';
                this.currentSessionId = null;
                this.updateStatus('Ready', '-', '-');
                break;
            case 'starting':
                this.startBtn.disabled = true;
                this.updateStatus('Starting...', '-', '-');
                break;
            case 'running':
                this.startBtn.style.display = 'none';
                this.stopBtn.style.display = 'inline-flex';
                this.progressSection.style.display = 'block';
                this.updateStatus('Running', this.currentSessionId, this.configuredDuration.textContent);
                break;
            case 'completed':
                this.startBtn.style.display = 'inline-flex';
                this.stopBtn.style.display = 'none';
                this.startBtn.disabled = false;
                this.updateStatus('Completed', this.currentSessionId, this.configuredDuration.textContent);
                break;
        }
    }

    updateStatus(status, sessionId, duration) {
        this.currentStatus.textContent = status;
        this.sessionIdDisplay.textContent = sessionId;
        this.configuredDuration.textContent = duration;
    }

    startProgressTracking() {
        this.progressInterval = setInterval(() => {
            if (!this.startTime || !this.estimatedDuration) return;

            const elapsed = Date.now() - this.startTime.getTime();
            const progress = Math.min((elapsed / this.estimatedDuration) * 100, 100);
            const remaining = Math.max(this.estimatedDuration - elapsed, 0);

            this.progressFill.style.width = `${progress}%`;
            this.progressPercent.textContent = `${Math.round(progress)}%`;
            
            if (remaining > 0) {
                const minutes = Math.floor(remaining / 60000);
                const seconds = Math.floor((remaining % 60000) / 1000);
                this.timeRemaining.textContent = `${minutes}:${seconds.toString().padStart(2, '0')} remaining`;
            } else {
                this.timeRemaining.textContent = 'Finalizing...';
            }
        }, 1000);
    }

    stopProgressTracking() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    addLog(message, level = 'info') {
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${level}`;
        
        const timestamp = new Date().toLocaleTimeString();
        logEntry.innerHTML = `
            <span class="log-time">[${timestamp}]</span>
            <span class="log-message">${message}</span>
        `;
        
        this.logsContainer.appendChild(logEntry);
        this.logsContainer.scrollTop = this.logsContainer.scrollHeight;
    }

    generateFinalReport() {
        const rawSizeText = this.rawSize.textContent;
        const compressedSizeText = this.compressedSize.textContent;
        const compressionRatioText = this.compressionRatio.textContent;
        const bandwidthSavingsText = this.bandwidthSavings.textContent;

        // Calculate space saved in bytes if we have the data
        let spaceSavedText = compressionRatioText;

        const reportHTML = `
            <div class="report-grid">
                <div class="report-item">
                    <h4>Original Size</h4>
                    <div class="value">${rawSizeText}</div>
                </div>
                <div class="report-item">
                    <h4>Compressed Size</h4>
                    <div class="value">${compressedSizeText}</div>
                </div>
                <div class="report-item">
                    <h4>Space Saved</h4>
                    <div class="value">${spaceSavedText}</div>
                </div>
                <div class="report-item">
                    <h4>Bandwidth Savings</h4>
                    <div class="value">${bandwidthSavingsText}</div>
                </div>
            </div>
            <div style="margin-top: 20px; padding: 15px; background: #2d2d2d; border-radius: 8px; border: 1px solid rgba(252, 149, 70, 0.2);">
                <h4 style="margin-bottom: 10px; color: #FC9546;">Summary</h4>
                <p style="color: #e2e8f0; line-height: 1.5;">Compression completed successfully. Files have been uploaded to S3 and local copies have been cleaned up.</p>
                <p style="margin-top: 10px; color: #FC9546;"><strong>Compression Results:</strong></p>
                <ul style="margin-left: 20px; margin-top: 5px; color: #639884; line-height: 1.6;">
                    <li>Original file size: <strong style="color: #e2e8f0;">${rawSizeText}</strong></li>
                    <li>Compressed file size: <strong style="color: #e2e8f0;">${compressedSizeText}</strong></li>
                    <li>Space saved: <strong style="color: #FC9546;">${spaceSavedText}</strong></li>
                    <li>Bandwidth reduction: <strong style="color: #FC9546;">${bandwidthSavingsText}</strong></li>
                </ul>
                <p style="margin-top: 15px; color: #FC9546;"><strong>S3 Buckets:</strong></p>
                <ul style="margin-left: 20px; margin-top: 5px; color: #639884;">
                    <li>Original: <code style="background: #1b1b1b; padding: 2px 6px; border-radius: 4px; color: #FC9546;">original-streams</code></li>
                    <li>Compressed: <code style="background: #1b1b1b; padding: 2px 6px; border-radius: 4px; color: #FC9546;">compressed-streams</code></li>
                </ul>
            </div>
        `;

        this.finalReport.innerHTML = reportHTML;
        this.reportSection.style.display = 'block';
        this.reportSection.classList.add('fade-in');
    }
}

// Initialize the dashboard when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new RTSPDashboard();
});

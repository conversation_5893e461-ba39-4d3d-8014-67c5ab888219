/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1b1b1b;
    min-height: 100vh;
    color: #e2e8f0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Cards */
.card {
    background: #2d2d2d;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(252, 149, 70, 0.2);
}

.card h2 {
    color: #FC9546;
    margin-bottom: 20px;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Form styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #FC9546;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #3d3d3d;
    border-radius: 8px;
    font-size: 1rem;
    background: #0d0d0d;
    color: #e2e8f0;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #FC9546;
    background: #1b1b1b;
    box-shadow: 0 0 0 3px rgba(252, 149, 70, 0.2);
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: #639884;
    font-size: 0.875rem;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-right: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, #FC9546 0%, #e8843a 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(252, 149, 70, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #639884 0%, #527a6b 100%);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(99, 152, 132, 0.4);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Status display */
.status-display {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background: #3d3d3d;
    border-radius: 6px;
}

.status-label {
    font-weight: 600;
    color: #FC9546;
}

.status-value {
    color: #639884;
    font-family: 'Courier New', monospace;
}

/* Metrics grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.metric-card {
    background: linear-gradient(135deg, #3d3d3d 0%, #2d2d2d 100%);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
    border: 1px solid rgba(252, 149, 70, 0.1);
}

.metric-card:hover {
    transform: translateY(-2px);
    border-color: rgba(252, 149, 70, 0.3);
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #FC9546 0%, #e8843a 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.metric-content h3 {
    color: #FC9546;
    font-size: 0.9rem;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #e2e8f0;
    margin-bottom: 2px;
}

.metric-label {
    font-size: 0.8rem;
    color: #639884;
}

/* Progress bar */
.progress-container {
    margin-top: 15px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #3d3d3d;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #FC9546 0%, #e8843a 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #639884;
}

/* Logs */
.logs-container {
    max-height: 300px;
    overflow-y: auto;
    background: #0d0d0d;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    border: 1px solid rgba(252, 149, 70, 0.2);
}

.log-entry {
    margin-bottom: 8px;
    display: flex;
    gap: 10px;
}

.log-time {
    color: #639884;
    white-space: nowrap;
}

.log-message {
    color: #e2e8f0;
    word-break: break-word;
}

.log-entry.error .log-message {
    color: #fc8181;
}

.log-entry.warning .log-message {
    color: #FC9546;
}

.log-entry.info .log-message {
    color: #639884;
}

/* Final report */
.final-report {
    background: #3d3d3d;
    border-radius: 8px;
    padding: 20px;
}

.report-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.report-item {
    text-align: center;
    padding: 15px;
    background: #2d2d2d;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    border: 1px solid rgba(252, 149, 70, 0.1);
}

.report-item h4 {
    color: #FC9546;
    margin-bottom: 8px;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.report-item .value {
    font-size: 1.3rem;
    font-weight: 700;
    color: #639884;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .status-display {
        grid-template-columns: 1fr;
    }
    
    .metric-card {
        flex-direction: column;
        text-align: center;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

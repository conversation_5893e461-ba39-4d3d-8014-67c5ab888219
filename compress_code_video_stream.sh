#!/bin/bash
# ─────────────────────────────────────────────
# Script: compress_code_video_stream_realtime.sh
# Purpose:
#   - Compress RTSP stream in real-time
#   - Output a single compressed .ts file
#   - Log compression stats and clean up
#   - Accept RTSP URL and duration as arguments
# ─────────────────────────────────────────────

# 📥 Parse input arguments
RTSP_URL="$1"
CAPTURE_DURATION="$2"

if [ -z "$RTSP_URL" ] || [ -z "$CAPTURE_DURATION" ]; then
    echo "❗ Usage: $0 <RTSP_URL> <DURATION (HH:MM:SS)>"
    exit 1
fi

# 📂 Create necessary directories
mkdir -p encoded/stream logs

# 🕒 Timestamp for filenames
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
ENCODED_FILE="encoded/stream/stream_${TIMESTAMP}_zmt.ts"
LOG_FILE="logs/compression_${TIMESTAMP}.log"

# 🔧 Cleanup function
cleanup() {
    echo "🧹 Cleaning up..." | tee -a "$LOG_FILE"
    rm -f encoded/stream/stream_${TIMESTAMP}_temp_*.ts
    wait 2>/dev/null
    echo "✅ Done" | tee -a "$LOG_FILE"
}

trap cleanup EXIT INT TERM

# 📊 Logging function
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 🚀 Start compression
start_realtime_compression() {
    log_message "🚀 Starting real-time compression"
    ffmpeg -y -hide_banner -loglevel warning \
        -rtsp_transport tcp \
        -fflags +genpts+igndts+discardcorrupt \
        -avoid_negative_ts make_zero \
        -analyzeduration 1000000 \
        -probesize 1000000 \
        -i "$RTSP_URL" \
        -vcodec libx264 \
        -preset veryslow \
        -b:v 197k \
        -maxrate 197k \
        -bufsize 394k \
        -c:a aac \
        -b:a 64k \
        -ac 2 \
        -ar 44100 \
        -t "$CAPTURE_DURATION" \
        -f mpegts "$ENCODED_FILE" 2>> "$LOG_FILE"

    if [ $? -eq 0 ] && [ -f "$ENCODED_FILE" ]; then
        encoded_size=$(stat -c %s "$ENCODED_FILE")
        encoded_size_human=$(du -h "$ENCODED_FILE" | cut -f1)
        log_message "✅ Compression complete: $encoded_size_human ($encoded_size bytes)"
    else
        log_message "❌ Compression failed"
        rm -f "$ENCODED_FILE"
        exit 1
    fi
}

# 📊 Generate final report
generate_report() {
    if [ ! -f "$ENCODED_FILE" ]; then return; fi

    ENCODED_SIZE_BYTES=$(stat -c %s "$ENCODED_FILE")
    ENCODED_SIZE_HUMAN=$(du -h "$ENCODED_FILE" | cut -f1)

    DURATION=$(ffprobe -v error -select_streams v:0 \
        -show_entries format=duration \
        -of default=noprint_wrappers=1:nokey=1 "$ENCODED_FILE" 2>/dev/null | awk '{printf "%.2f", $1}')

    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "📊 COMPRESSION REPORT - $(date)"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "📦 File: $ENCODED_FILE"
    echo "📉 Size: $ENCODED_SIZE_HUMAN ($ENCODED_SIZE_BYTES bytes)"
    echo "⏱  Duration: $DURATION seconds"
    echo "📋 Log File: $LOG_FILE"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    {
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo "📊 FINAL COMPRESSION REPORT"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo "Size: $ENCODED_SIZE_HUMAN ($ENCODED_SIZE_BYTES bytes)"
        echo "Duration: $DURATION seconds"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    } >> "$LOG_FILE"
}

# 🎬 Main execution
main() {
    log_message "🎬 Starting real-time RTSP stream compression"
    log_message "📡 RTSP URL: $RTSP_URL"
    log_message "📦 Output file: $ENCODED_FILE"

    start_realtime_compression
    generate_report
}

main "$@"


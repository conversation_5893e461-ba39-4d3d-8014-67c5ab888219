#!/bin/bash
# ─────────────────────────────────────────────
# Script: compress_code_video_stream_realtime.sh
# Purpose:
#   - Compress RTSP stream in real-time
#   - Output a single compressed .ts file
#   - Log compression stats and clean up
#   - Accept RTSP URL and duration as arguments
# ─────────────────────────────────────────────

# 📥 Parse input arguments
RTSP_URL="$1"
CAPTURE_DURATION="$2"
SHARED_TIMESTAMP="$3"

if [ -z "$RTSP_URL" ] || [ -z "$CAPTURE_DURATION" ]; then
    echo "❗ Usage: $0 <RTSP_URL> <DURATION (HH:MM:SS)> [TIMESTAMP]"
    exit 1
fi

# 📂 Create necessary directories
mkdir -p encoded/stream logs

# 🕒 Timestamp for filenames (use shared timestamp if provided)
if [ -n "$SHARED_TIMESTAMP" ]; then
    TIMESTAMP="$SHARED_TIMESTAMP"
else
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
fi
ENCODED_FILE="encoded/stream/stream_${TIMESTAMP}_zmt.ts"
HLS_PLAYLIST="encoded/stream/stream_${TIMESTAMP}_zmt.m3u8"
HLS_SEGMENT_PATTERN="encoded/stream/stream_${TIMESTAMP}_zmt_%03d.ts"
LOG_FILE="logs/compression_${TIMESTAMP}.log"

# 🔧 Cleanup function
cleanup() {
    echo "🧹 Cleaning up..." | tee -a "$LOG_FILE"
    rm -f encoded/stream/stream_${TIMESTAMP}_temp_*.ts
    wait 2>/dev/null
    echo "✅ Done" | tee -a "$LOG_FILE"
}

trap cleanup EXIT INT TERM

# 📊 Logging function
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 🚀 Start compression with HLS output
start_realtime_compression() {
    log_message "🚀 Starting real-time compression with HLS output"

    # Create single file output first
    ffmpeg -y -hide_banner -loglevel warning \
        -rtsp_transport tcp \
        -fflags +genpts+igndts+discardcorrupt \
        -avoid_negative_ts make_zero \
        -analyzeduration 1000000 \
        -probesize 1000000 \
        -i "$RTSP_URL" \
        -vcodec libx264 \
        -preset veryslow \
        -b:v 197k \
        -maxrate 197k \
        -bufsize 394k \
        -c:a aac \
        -b:a 64k \
        -ac 2 \
        -ar 44100 \
        -t "$CAPTURE_DURATION" \
        -f mpegts "$ENCODED_FILE" \
        2>> "$LOG_FILE" &

    # Create HLS playlist output in parallel
    ffmpeg -y -hide_banner -loglevel warning \
        -rtsp_transport tcp \
        -fflags +genpts+igndts+discardcorrupt \
        -avoid_negative_ts make_zero \
        -analyzeduration 1000000 \
        -probesize 1000000 \
        -i "$RTSP_URL" \
        -vcodec libx264 \
        -preset veryslow \
        -b:v 197k \
        -maxrate 197k \
        -bufsize 394k \
        -c:a aac \
        -b:a 64k \
        -ac 2 \
        -ar 44100 \
        -t "$CAPTURE_DURATION" \
        -f hls \
        -hls_time 6 \
        -hls_list_size 10 \
        -hls_flags delete_segments+append_list \
        "$HLS_PLAYLIST" \
        2>> "$LOG_FILE" &

    # Wait for both processes to complete
    wait

    if [ $? -eq 0 ] && [ -f "$ENCODED_FILE" ]; then
        encoded_size=$(stat -c %s "$ENCODED_FILE")
        encoded_size_human=$(du -h "$ENCODED_FILE" | cut -f1)
        log_message "✅ Compression complete: $encoded_size_human ($encoded_size bytes)"

        # Check if HLS playlist was created
        if [ -f "$HLS_PLAYLIST" ]; then
            log_message "✅ HLS playlist created: $HLS_PLAYLIST"
        else
            log_message "⚠️ HLS playlist not created, but single file compression succeeded"
        fi
    else
        log_message "❌ Compression failed"
        rm -f "$ENCODED_FILE" "$HLS_PLAYLIST"
        exit 1
    fi
}

# 📊 Generate final report
generate_report() {
    if [ ! -f "$ENCODED_FILE" ]; then return; fi

    ENCODED_SIZE_BYTES=$(stat -c %s "$ENCODED_FILE")
    ENCODED_SIZE_HUMAN=$(du -h "$ENCODED_FILE" | cut -f1)

    DURATION=$(ffprobe -v error -select_streams v:0 \
        -show_entries format=duration \
        -of default=noprint_wrappers=1:nokey=1 "$ENCODED_FILE" 2>/dev/null | awk '{printf "%.2f", $1}')

    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "📊 COMPRESSION REPORT - $(date)"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "📦 File: $ENCODED_FILE"
    echo "📉 Size: $ENCODED_SIZE_HUMAN ($ENCODED_SIZE_BYTES bytes)"
    echo "⏱  Duration: $DURATION seconds"
    echo "📋 Log File: $LOG_FILE"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    {
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo "📊 FINAL COMPRESSION REPORT"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo "Size: $ENCODED_SIZE_HUMAN ($ENCODED_SIZE_BYTES bytes)"
        echo "Duration: $DURATION seconds"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    } >> "$LOG_FILE"
}

# 🎬 Main execution
main() {
    log_message "🎬 Starting real-time RTSP stream compression"
    log_message "📡 RTSP URL: $RTSP_URL"
    log_message "📦 Output file: $ENCODED_FILE"

    start_realtime_compression
    generate_report
}

main "$@"


#!/bin/bash
# ─────────────────────────────────────────────
# Script: run_both_streams.sh
# Purpose:
#   - Capture both raw and compressed RTSP streams
#   - Store locally using a shared timestamp
#   - Upload all .ts files from each folder to S3
#   - Delete local files after successful upload
# ─────────────────────────────────────────────

# 📥 Input Arguments
RTSP_URL="$1"
CAPTURE_DURATION="$2"

if [ -z "$RTSP_URL" ] || [ -z "$CAPTURE_DURATION" ]; then
    echo "❗ Usage: $0 <RTSP_URL> <DURATION (HH:MM:SS)>"
    exit 1
fi

# 🕒 Shared timestamp and directory setup
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
DATE_PATH=$(date +"%Y/%m/%d")
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
RAW_DIR="$SCRIPT_DIR/original/stream"
ENCODED_DIR="$SCRIPT_DIR/encoded/stream"

# 🪣 S3 bucket names
RAW_BUCKET_NAME="original-streams"
COMPRESSED_BUCKET_NAME="compressed-streams"

# ☁️ Get AWS region
AWS_REGION=$(aws configure get region)
if [ -z "$AWS_REGION" ]; then
    echo "❌ AWS CLI region not configured. Run: aws configure"
    exit 1
fi

# 🛑 Handle script interruption
cleanup_on_interrupt() {
    log "🛑 Interrupted. Cleaning up..."
    kill $COMPRESS_PID $RAW_PID 2>/dev/null
    exit 1
}
trap cleanup_on_interrupt INT TERM

# 🕓 Timestamped logger
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# ☁️ Create bucket if it doesn’t exist
ensure_bucket() {
    BUCKET_NAME=$1
    if ! aws s3api head-bucket --bucket "$BUCKET_NAME" 2>/dev/null; then
        log "🪣 Creating bucket: $BUCKET_NAME"
        if [ "$AWS_REGION" == "us-east-1" ]; then
            aws s3api create-bucket --bucket "$BUCKET_NAME"
        else
            aws s3api create-bucket --bucket "$BUCKET_NAME" \
                --region "$AWS_REGION" \
                --create-bucket-configuration LocationConstraint="$AWS_REGION"
        fi
    else
        log "✅ Bucket exists: $BUCKET_NAME"
    fi
}

# ☁️ Upload all .ts files in folder to S3 and delete locally
upload_and_cleanup_folder() {
    LOCAL_DIR="$1"
    BUCKET="$2"

    if [ -d "$LOCAL_DIR" ]; then
        for FILE in "$LOCAL_DIR"/*.ts; do
            [ -e "$FILE" ] || continue  # skip if no files
            DEST_PATH="s3://$BUCKET/$DATE_PATH/$(basename "$FILE")"
            log "⬆️ Uploading $FILE to $DEST_PATH"
            if aws s3 cp "$FILE" "$DEST_PATH"; then
                log "✅ Uploaded. Deleting $FILE"
                rm -f "$FILE"
            else
                log "❌ Upload failed for $FILE"
            fi
        done
    else
        log "❌ Directory not found: $LOCAL_DIR"
    fi
}

# 🪣 Ensure buckets exist
ensure_bucket "$RAW_BUCKET_NAME"
ensure_bucket "$COMPRESSED_BUCKET_NAME"

# 🚀 Start both capture scripts with shared timestamp
log "▶️ Starting capture with timestamp $TIMESTAMP"

"$SCRIPT_DIR/compress_code_video_stream.sh" "$RTSP_URL" "$CAPTURE_DURATION" "$TIMESTAMP" &
COMPRESS_PID=$!

"$SCRIPT_DIR/save_rtsp_raw_stream.sh" "$RTSP_URL" "$CAPTURE_DURATION" "$TIMESTAMP" &
RAW_PID=$!

# 🧘 Wait for both to complete
wait $COMPRESS_PID
wait $RAW_PID

log "✅ Both capture processes completed."

# ☁️ Upload and cleanup all relevant files
upload_and_cleanup_folder "$RAW_DIR" "$RAW_BUCKET_NAME"
upload_and_cleanup_folder "$ENCODED_DIR" "$COMPRESSED_BUCKET_NAME"

log "🏁 All done."


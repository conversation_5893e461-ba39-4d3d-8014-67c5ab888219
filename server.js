const express = require('express');
const cors = require('cors');
const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const WebSocket = require('ws');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// WebSocket server for real-time updates
const wss = new WebSocket.Server({ port: 8080 });

// Store active processes and their metrics
let activeProcesses = new Map();
let processMetrics = new Map();

// Utility function to convert minutes to HH:MM:SS
function minutesToDuration(minutes) {
    const totalSeconds = Math.floor(minutes * 60);
    const hours = Math.floor(totalSeconds / 3600);
    const mins = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// Utility function to get total size of .ts files in a folder
function getFolderSize(folderPath) {
    return new Promise((resolve) => {
        exec(`find "${folderPath}" -name "*.ts" -type f -exec stat -c %s {} + 2>/dev/null | awk '{sum+=$1} END {print sum+0}'`, (error, stdout) => {
            if (error) {
                resolve(0);
            } else {
                const size = parseInt(stdout.trim()) || 0;
                resolve(size);
            }
        });
    });
}

// Utility function to format bytes
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Sanitize RTSP URL to prevent command injection
function sanitizeRtspUrl(url) {
    // Basic validation - should start with rtsp:// and contain valid characters
    const rtspRegex = /^rtsp:\/\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=%]+$/;
    if (!rtspRegex.test(url)) {
        throw new Error('Invalid RTSP URL format');
    }
    return url;
}

// Parse output for file size metrics
function parseOutputForMetrics(output, sessionId) {
    if (!processMetrics.has(sessionId)) {
        processMetrics.set(sessionId, { originalSize: 0, encodedSize: 0 });
    }

    const metrics = processMetrics.get(sessionId);

    // Look for compression completion with file size
    const compressionMatch = output.match(/✅ Compression complete: .+ \((\d+) bytes\)/);
    if (compressionMatch) {
        metrics.encodedSize = parseInt(compressionMatch[1]);
        console.log(`Parsed encoded size: ${metrics.encodedSize} bytes`);
    }

    // Look for file size information in upload output
    const uploadMatch = output.match(/upload: (.+) to s3:\/\/(.+)/);
    if (uploadMatch) {
        const filePath = uploadMatch[1];

        // Get file size before it's deleted
        if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            const fileSize = stats.size;

            if (filePath.includes('original/stream/')) {
                metrics.originalSize = fileSize;
                console.log(`Parsed original size: ${metrics.originalSize} bytes`);
            } else if (filePath.includes('encoded/stream/')) {
                // Double-check encoded size from file if not already set
                if (metrics.encodedSize === 0) {
                    metrics.encodedSize = fileSize;
                    console.log(`Parsed encoded size from file: ${metrics.encodedSize} bytes`);
                }
            }
        }
    }

    // Broadcast updated metrics if we have any data
    if (metrics.originalSize > 0 || metrics.encodedSize > 0) {
        const compressionRatio = metrics.originalSize > 0 ?
            ((metrics.originalSize - metrics.encodedSize) / metrics.originalSize * 100) : 0;

        broadcast({
            type: 'progress',
            sessionId,
            data: {
                originalSize: metrics.originalSize,
                encodedSize: metrics.encodedSize,
                originalSizeFormatted: formatBytes(metrics.originalSize),
                encodedSizeFormatted: formatBytes(metrics.encodedSize),
                compressionRatio: compressionRatio.toFixed(2),
                timestamp: new Date().toISOString()
            }
        });
    }
}

// Broadcast message to all connected WebSocket clients
function broadcast(message) {
    wss.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify(message));
        }
    });
}

// Monitor folder sizes and broadcast updates
async function monitorProgress(sessionId) {
    const originalPath = path.join(__dirname, 'original', 'stream');
    const encodedPath = path.join(__dirname, 'encoded', 'stream');

    console.log(`Starting monitoring for session ${sessionId}`);
    console.log(`Original path: ${originalPath}`);
    console.log(`Encoded path: ${encodedPath}`);

    const interval = setInterval(async () => {
        try {
            const originalSize = await getFolderSize(originalPath);
            const encodedSize = await getFolderSize(encodedPath);

            console.log(`Session ${sessionId} - Original: ${originalSize} bytes, Encoded: ${encodedSize} bytes`);

            const compressionRatio = originalSize > 0 ? ((originalSize - encodedSize) / originalSize * 100) : 0;

            const update = {
                type: 'progress',
                sessionId,
                data: {
                    originalSize,
                    encodedSize,
                    originalSizeFormatted: formatBytes(originalSize),
                    encodedSizeFormatted: formatBytes(encodedSize),
                    compressionRatio: compressionRatio.toFixed(2),
                    timestamp: new Date().toISOString()
                }
            };

            broadcast(update);
        } catch (error) {
            console.error('Error monitoring progress:', error);
        }
    }, 2000); // Update every 2 seconds

    return interval;
}

// API Routes

// Start compression process
app.post('/api/start-compression', async (req, res) => {
    try {
        const { rtspUrl, durationMinutes } = req.body;
        
        // Validate inputs
        if (!rtspUrl || !durationMinutes) {
            return res.status(400).json({ error: 'RTSP URL and duration are required' });
        }
        
        if (durationMinutes <= 0 || durationMinutes > 15) {
            return res.status(400).json({ error: 'Duration must be between 1 and 15 minutes' });
        }
        
        // Sanitize RTSP URL
        const sanitizedUrl = sanitizeRtspUrl(rtspUrl);
        const duration = minutesToDuration(durationMinutes);
        
        // Generate session ID
        const sessionId = Date.now().toString();
        
        // Check if another process is running
        if (activeProcesses.size > 0) {
            return res.status(409).json({ error: 'Another compression process is already running' });
        }
        
        // Start the shell script
        const scriptPath = path.join(__dirname, 'run_both_streams.sh');
        const process = spawn('bash', [scriptPath, sanitizedUrl, duration], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        activeProcesses.set(sessionId, process);
        
        // Start monitoring progress
        const monitorInterval = monitorProgress(sessionId);
        
        // Handle process completion
        process.on('close', (code) => {
            clearInterval(monitorInterval);
            activeProcesses.delete(sessionId);

            // Send final metrics if available
            if (processMetrics.has(sessionId)) {
                const metrics = processMetrics.get(sessionId);
                const compressionRatio = metrics.originalSize > 0 ?
                    ((metrics.originalSize - metrics.encodedSize) / metrics.originalSize * 100) : 0;

                broadcast({
                    type: 'progress',
                    sessionId,
                    data: {
                        originalSize: metrics.originalSize,
                        encodedSize: metrics.encodedSize,
                        originalSizeFormatted: formatBytes(metrics.originalSize),
                        encodedSizeFormatted: formatBytes(metrics.encodedSize),
                        compressionRatio: compressionRatio.toFixed(2),
                        timestamp: new Date().toISOString()
                    }
                });
            }

            broadcast({
                type: 'completed',
                sessionId,
                data: {
                    exitCode: code,
                    success: code === 0,
                    timestamp: new Date().toISOString()
                }
            });

            // Clean up metrics
            processMetrics.delete(sessionId);
        });
        
        // Handle process errors
        process.on('error', (error) => {
            clearInterval(monitorInterval);
            activeProcesses.delete(sessionId);
            processMetrics.delete(sessionId);

            broadcast({
                type: 'error',
                sessionId,
                data: {
                    error: error.message,
                    timestamp: new Date().toISOString()
                }
            });
        });
        
        // Log process output and extract file size information
        process.stdout.on('data', (data) => {
            const output = data.toString();
            console.log(`Process output: ${output}`);

            // Parse file size information from the output
            parseOutputForMetrics(output, sessionId);

            broadcast({
                type: 'log',
                sessionId,
                data: {
                    message: output,
                    timestamp: new Date().toISOString()
                }
            });
        });
        
        process.stderr.on('data', (data) => {
            console.error(`Process error: ${data}`);
            broadcast({
                type: 'log',
                sessionId,
                data: {
                    message: data.toString(),
                    level: 'error',
                    timestamp: new Date().toISOString()
                }
            });
        });
        
        res.json({
            success: true,
            sessionId,
            message: 'Compression process started',
            duration,
            estimatedCompletionTime: new Date(Date.now() + durationMinutes * 60 * 1000).toISOString()
        });
        
    } catch (error) {
        console.error('Error starting compression:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get current status
app.get('/api/status', (req, res) => {
    res.json({
        activeProcesses: activeProcesses.size,
        sessions: Array.from(activeProcesses.keys())
    });
});

// Stop compression process
app.post('/api/stop-compression/:sessionId', (req, res) => {
    const { sessionId } = req.params;
    const process = activeProcesses.get(sessionId);
    
    if (!process) {
        return res.status(404).json({ error: 'Session not found' });
    }
    
    process.kill('SIGTERM');
    activeProcesses.delete(sessionId);
    
    res.json({ success: true, message: 'Process stopped' });
});

// WebSocket connection handling
wss.on('connection', (ws) => {
    console.log('WebSocket client connected');
    
    ws.on('close', () => {
        console.log('WebSocket client disconnected');
    });
    
    ws.on('error', (error) => {
        console.error('WebSocket error:', error);
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 RTSP Compression Dashboard server running on port ${PORT}`);
    console.log(`📊 WebSocket server running on port 8080`);
    console.log(`🌐 Open http://localhost:${PORT} to access the dashboard`);
});
